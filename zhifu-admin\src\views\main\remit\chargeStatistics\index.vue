<template>
  <div class="PostMan-container">
    <el-card style="max-height: 825px;overflow-y: auto;">
      <div style="display: flex; align-items: center; gap: 10px;">
        <el-select v-model="method" style="width: 100px;">
          <el-option label="GET" value="GET" />
          <el-option label="POST" value="POST" />
        </el-select>
        <el-input v-model="url" placeholder="接口地址" style="flex: 1;" />
        <el-button type="primary" @click="sendRequest" :loading="loading" style="width: 80px;">发送</el-button>
      </div>

      <el-tabs v-model="activeTab" style="margin-top: 20px;">
        <el-tab-pane label="Params" name="params">
          <el-table :data="params" style="width: 100%">
            <el-table-column label="Key">
              <template #default="scope">
                <el-input v-model="scope.row.key" />
              </template>
            </el-table-column>
            <el-table-column label="Value">
              <template #default="scope">
                <el-input v-model="scope.row.value" />
              </template>
            </el-table-column>
            <el-table-column width="60">
              <template #default="scope">
                <el-button type="danger" icon="el-icon-delete" @click="removeParam(scope.$index)" circle />
              </template>
            </el-table-column>
          </el-table>
          <el-button type="primary" @click="addParam" style="margin-top: 10px;">添加</el-button>
        </el-tab-pane>
        <el-tab-pane label="Headers" name="headers">
          <el-table :data="headers" style="width: 100%">
            <el-table-column label="Key">
              <template #default="scope">
                <el-input v-model="scope.row.key" />
              </template>
            </el-table-column>
            <el-table-column label="Value">
              <template #default="scope">
                <el-input v-model="scope.row.value" />
              </template>
            </el-table-column>
            <el-table-column width="60">
              <template #default="scope">
                <el-button type="danger" icon="el-icon-delete" @click="removeHeader(scope.$index)" circle />
              </template>
            </el-table-column>
          </el-table>
          <el-button type="primary" @click="addHeader" style="margin-top: 10px;">添加</el-button>
        </el-tab-pane>
        <el-tab-pane label="Body" name="body" v-if="method === 'POST'">
          <el-input
            type="textarea"
            v-model="body"
            :rows="8"
            placeholder="请输入JSON格式的请求体"
          />
        </el-tab-pane>
      </el-tabs>

      <el-divider>响应结果</el-divider>
      <div v-loading="loading" element-loading-text="请求中...">
        <el-input
          type="textarea"
          :value="response"
          :rows="20"
          readonly
          style="font-family: monospace;"
        />
        <div style="margin-top: 10px;">
          <el-input
            v-model="nodeName"
            placeholder="请输入节点名称"
            style="width: 300px; margin-right: 10px;" clearable
          />
          <el-input
            v-model="tableName"
            placeholder="表名（中文）"
            style="width: 300px; margin-right: 10px;" clearable
          />
          <el-button type="primary" @click="SaveData" :disabled="!nodeName || !tableName" >保存数据</el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup name="PostMan">
import { ref } from 'vue'
import axios from 'axios'
import { ElMessage } from 'element-plus'
import { SavePostManData } from '/@/api/main/datInterfaceM';

const method = ref('GET')
const url = ref('')
const params = ref([{ key: '', value: '' }])
const headers = ref([{ key: '', value: '' }])
const body = ref('')
const response = ref('')
const activeTab = ref('params')
const loading = ref(false)
const savedData = ref<any[]>([])
const nodeName = ref('')
const tableName = ref('')

const addParam = () => {
  params.value.push({ key: '', value: '' })
}
const removeParam = (idx: number) => {
  params.value.splice(idx, 1)
}
const addHeader = () => {
  headers.value.push({ key: '', value: '' })
}
const removeHeader = (idx: number) => {
  headers.value.splice(idx, 1)
}

const sendRequest = async () => {
  try {
    loading.value = true
    const paramsObj = Object.fromEntries(params.value.filter(i => i.key).map(i => [i.key, i.value]))
    const headersObj = Object.fromEntries(headers.value.filter(i => i.key).map(i => [i.key, i.value]))
    let res
    if (method.value === 'GET') {
      res = await axios.get(url.value, { params: paramsObj, headers: headersObj })
    } else {
      let data = {}
      try {
        data = body.value ? JSON.parse(body.value) : {}
      } catch (e) {
        response.value = '请求体不是合法的JSON'
        return
      }
      res = await axios.post(url.value, data, { params: paramsObj, headers: headersObj })
    }
    response.value = JSON.stringify(res.data, null, 2)
  } catch (err: any) {
    response.value = err.message
  } finally {
    loading.value = false
  }
}

//保存数据
const SaveData = async () => {
  try {
    if (!response.value) {
      ElMessage.warning('没有可保存的响应数据')
      return
    }

    if (!nodeName.value) {
      ElMessage.warning('请输入节点名称')
      return
    }

    const responseData = JSON.parse(response.value)
    
    // 递归查找数据结果集
    const findDataArray = (obj: any): any[] | null => {
      // 如果本身就是数组，直接返回
      if (Array.isArray(obj)) {
        return obj
      }
      
      // 如果是对象，遍历所有属性
      if (typeof obj === 'object' && obj !== null) {
        // 优先检查常见的返回结果字段
        const commonResultFields = ['result', 'data', 'list', 'items', 'records', 'rows']
        for (const field of commonResultFields) {
          if (Array.isArray(obj[field])) {
            return obj[field]
          }
        }
        
        // 递归检查所有属性
        for (const key in obj) {
          const result = findDataArray(obj[key])
          if (result) {
            return result
          }
        }
      }
      
      return null
    }

    const dataArray = findDataArray(responseData)
    
    if (dataArray) {
      savedData.value = dataArray
      console.log('找到的数据结果集：', savedData.value)

      // 将数据转换为字典格式
      const inputs: { [key: string]: string } = {}
      if (dataArray.length > 0) {
        const firstItem = dataArray[0]
        Object.keys(firstItem).forEach((key, index) => {
          inputs[`Fld_${index + 1}`] = key
        })
      }
      
      // 发送数据到后端
      const res = await SavePostManData({
        inputs: inputs,
        tableName: tableName.value,
        data: dataArray
      });
      console.log(res)
      if(res.data.result.message != '保存成功！'){
        ElMessage({
          message: res.data.result.message,
          type: "error",
        });
        return false
      }
      else ElMessage.success('数据保存成功')
    } else {
        ElMessage.warning('未找到可保存的数据结果集')
    }
  } catch (error) {
    ElMessage.error('保存数据失败：' + (error as Error).message)
  }
}
</script>

<style scoped>
:deep(.el-ipnut),
:deep(.el-select),
:deep(.el-input-number) {
  width: 100%;
}
</style>

