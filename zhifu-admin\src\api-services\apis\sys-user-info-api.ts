/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。前后端分离架构(.NET6/Vue3)，开箱即用紧随前沿技术。<br/><a href='https://gitee.com/zuohuaijun/Admin.NET/'>https://gitee.com/zuohuaijun/Admin.NET</a>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { UserInfo } from '../models';
import { AdminResultUserInfo } from '../models';
import { AdminResultListUserInfo } from '../models';
import { AdminResultInt64 } from '../models';

/**
 * SysUserInfoApi - axios parameter creator
 * @export
 */
export const SysUserInfoApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加用户信息
         * @param {UserInfo} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUserInfoAddPost: async (body?: UserInfo, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/UserInfo/add`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions: AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取用户信息列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUserInfoListGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/UserInfo/list`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions: AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 更新用户信息
         * @param {UserInfo} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUserInfoUpdatePost: async (body?: UserInfo, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/UserInfo/update`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions: AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 删除用户信息
         * @param {string} userId 用户ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUserInfoDeletePost: async (userId: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'userId' is not null or undefined
            if (userId === null || userId === undefined) {
                throw new RequiredError('userId','Required parameter userId was null or undefined when calling apiUserInfoDeletePost.');
            }
            const localVarPath = `/api/UserInfo/delete`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions: AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (userId !== undefined) {
                localVarQueryParameter['userId'] = userId;
            }

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * SysUserInfoApi - functional programming interface
 * @export
 */
export const SysUserInfoApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 增加用户信息
         * @param {UserInfo} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserInfoAddPost(body?: UserInfo, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt64>>> {
            const localVarAxiosArgs = await SysUserInfoApiAxiosParamCreator(configuration).apiUserInfoAddPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs: AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取用户信息列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserInfoListGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultListUserInfo>>> {
            const localVarAxiosArgs = await SysUserInfoApiAxiosParamCreator(configuration).apiUserInfoListGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs: AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 更新用户信息
         * @param {UserInfo} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserInfoUpdatePost(body?: UserInfo, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt64>>> {
            const localVarAxiosArgs = await SysUserInfoApiAxiosParamCreator(configuration).apiUserInfoUpdatePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs: AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 删除用户信息
         * @param {string} userId 用户ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserInfoDeletePost(userId: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultInt64>>> {
            const localVarAxiosArgs = await SysUserInfoApiAxiosParamCreator(configuration).apiUserInfoDeletePost(userId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs: AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * SysUserInfoApi - factory interface
 * @export
 */
export const SysUserInfoApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 增加用户信息
         * @param {UserInfo} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserInfoAddPost(body?: UserInfo, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt64>> {
            return SysUserInfoApiFp(configuration).apiUserInfoAddPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取用户信息列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserInfoListGet(options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultListUserInfo>> {
            return SysUserInfoApiFp(configuration).apiUserInfoListGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 更新用户信息
         * @param {UserInfo} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserInfoUpdatePost(body?: UserInfo, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt64>> {
            return SysUserInfoApiFp(configuration).apiUserInfoUpdatePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 删除用户信息
         * @param {string} userId 用户ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiUserInfoDeletePost(userId: string, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultInt64>> {
            return SysUserInfoApiFp(configuration).apiUserInfoDeletePost(userId, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * SysUserInfoApi - object-oriented interface
 * @export
 * @class SysUserInfoApi
 * @extends {BaseAPI}
 */
export class SysUserInfoApi extends BaseAPI {
    /**
     * 
     * @summary 增加用户信息
     * @param {UserInfo} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysUserInfoApi
     */
    public async apiUserInfoAddPost(body?: UserInfo, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt64>> {
        return SysUserInfoApiFp(this.configuration).apiUserInfoAddPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取用户信息列表
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysUserInfoApi
     */
    public async apiUserInfoListGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultListUserInfo>> {
        return SysUserInfoApiFp(this.configuration).apiUserInfoListGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 更新用户信息
     * @param {UserInfo} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysUserInfoApi
     */
    public async apiUserInfoUpdatePost(body?: UserInfo, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt64>> {
        return SysUserInfoApiFp(this.configuration).apiUserInfoUpdatePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 删除用户信息
     * @param {string} userId 用户ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SysUserInfoApi
     */
    public async apiUserInfoDeletePost(userId: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultInt64>> {
        return SysUserInfoApiFp(this.configuration).apiUserInfoDeletePost(userId, options).then((request) => request(this.axios, this.basePath));
    }
}