<template>
	<div class="userManage-container">
		<el-card shadow="hover" :body-style="{ paddingBottom: '0' }">
			<el-form :model="queryParams" ref="queryForm" labelWidth="110">
				<el-row>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10">
						<el-form-item label="客户名称">
							<el-input v-model="queryParams.cusName" clearable="" placeholder="请输入客户名称" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10">
						<el-form-item label="数据库服务器IP">
							<el-input v-model="queryParams.dBIP" clearable="" placeholder="请输入数据库服务器IP" />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10">
						<el-form-item label="数据库名称">
							<el-input v-model="queryParams.dBName" clearable="" placeholder="请输入数据库名称" />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6" class="mb10">
						<el-form-item>
							<el-button-group style="display: flex; align-items: center">
								<el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'basCustomer:page'"> 查询 </el-button>
								<el-button icon="ele-Refresh" @click="handleReset" v-auth="'basCustomer:page'"> 重置 </el-button>
								<!-- <el-button icon="ele-ZoomIn" @click="changeAdvanceQueryUI" v-if="!showAdvanceQueryUI" style="margin-left:5px;"> 高级查询 </el-button> -->
								<!-- <el-button icon="ele-ZoomOut" @click="changeAdvanceQueryUI" v-if="showAdvanceQueryUI" style="margin-left:5px;"> 隐藏 </el-button> -->
								<el-button type="primary" style="margin-left: 5px" icon="ele-Plus" @click="openAddBasCustomer" v-auth="'basCustomer:add'"> 新增 </el-button>
							</el-button-group>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
		</el-card>
		<el-card class="full-table" shadow="hover" style="margin-top: 5px">
			<el-table :data="tableData" style="width: 100%" v-loading="loading" tooltip-effect="light" row-key="id" @sort-change="sortChange" border="">
				<el-table-column type="index" label="序号" width="55" align="center" />
				<el-table-column prop="userId" label="用户ID" width="140" show-overflow-tooltip="" />
				<el-table-column prop="loginID" label="登录名" width="140" show-overflow-tooltip="" />
				<el-table-column prop="roleType" label="角色类型" width="140" show-overflow-tooltip="" />
				<el-table-column prop="organization" label="单位名称" width="140" show-overflow-tooltip="" />
				<el-table-column
					label="操作"
					width="200"
					align="center"
					fixed="right"
					show-overflow-tooltip=""
				>
					<template #default="scope">
						<!-- <el-button v-if="!scope.row.isDatabaseCreated" icon="ele-Coin" size="small" text type="danger" @click="createTenant(scope.row)"> 创建库 </el-button>
						<el-button v-else disabled icon="ele-Coin" size="small" text type="danger"> 已创建 </el-button> -->
						<el-button icon="ele-Edit" size="small" text="" type="primary" @click="openEditBasCustomer(scope.row)" v-auth="'userManage:update'"> 编辑 </el-button>
						<el-button icon="ele-Delete" size="small" text="" type="primary" @click="delBasCustomer(scope.row)" v-auth="'userManage:delete'"> 删除 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination
				v-model:currentPage="tableParams.page"
				v-model:page-size="tableParams.pageSize"
				:total="tableParams.total"
				:page-sizes="[10, 20, 50, 100, 200, 500]"
				small=""
				background=""
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
				layout="total, sizes, prev, pager, next, jumper"
			/>
			<printDialog ref="printDialogRef" :title="printBasCustomerTitle" @reloadTable="handleQuery" />
			<!-- <editDialog ref="editDialogRef" :title="editBasCustomerTitle" @reloadTable="handleQuery" /> -->
		</el-card>
	</div>
</template>

<script lang="ts" setup="" name="basCustomer">
import { ref, onMounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { auth } from '/@/utils/authFunction';
import { getDictDataItem as di, getDictDataList as dl } from '/@/utils/dict-utils';
import { formatDate } from '/@/utils/formatTime';

import printDialog from '/@/views/system/print/component/hiprint/preview.vue';
import editDialog from '/@/views/main/basCustomer/component/editDialog.vue';
import { pageBasCustomer, deleteBasCustomer, DbServerIP, CreateDatabase, DeleteDatabase, updateBasCustomer } from '/@/api/main/basCustomer';
import { pageUserInfo } from '/@/api/main/userInfo';
import { SelectValue } from '/@/api/main/datInterfaceM';

const showAdvanceQueryUI = ref(false);
const printDialogRef = ref();
const editDialogRef = ref();
const loading = ref(false);
const tableData = ref<any[]>([]);
const queryParams = ref<any>({});
const tableParams = ref({
	page: 1,
	pageSize: 10,
	total: 0,
});

const printBasCustomerTitle = ref('');
const editBasCustomerTitle = ref('');

// 改变高级查询的控件显示状态
const changeAdvanceQueryUI = () => {
	showAdvanceQueryUI.value = !showAdvanceQueryUI.value;
};

// 查询操作
const handleQuery = async () => {
	loading.value = true;
	var res = await pageUserInfo();
	console.log("111111",res)
	// var res = await pageBasCustomer(Object.assign(queryParams.value, tableParams.value));
	tableData.value = res.data.userList;
	// console.log("222222222222222222222222222222222"+tableData.value);
	tableParams.value.total = res.data.total;
	//tableData.value.forEach((row) => {
	// 	if (row.ifCreat === '1') {
	// 		row.isDatabaseCreated = true;
	// 		row.disabled = true;
	// 	} else if (row.ifCreat === '0') {
	// 		row.isDatabaseCreated = false;
	// 		row.disabled = false;
	// 	}
	// });
	loading.value = false;
};

//重置
const handleReset = () => {
	// 清空查询条件
	// Object.keys(queryParams.value).forEach((key) => {
	// 	queryParams.value[key] = '';
	// });
	// 重新加载表格数据
	handleQuery();
};

// // 创建数据库
// const createTenant = async (row: any) => {
// 	const { dbip, dbName, dbType, ifCreat,databaseName } = row; // 获取服务器IP和数据库名称和数据库类型
// 	console.log(row);
// 	debugger;
// 	if (!dbip) {
// 		ElMessage.error('服务器IP为空');
// 		return;
// 	}
// 	const confirmResult = await confirmDialog(`确定创建/更新客户数据库吗？`, '提示');
// 	if (!confirmResult) {
// 		return;
// 	}
// 	// 从服务器获取登录用户信息
// 	const res = await DbServerIP();
// //	const serverInfo = res.data.result.sysDbServer.find((server) => server.dbip === dbip);
// //	const { logName, logPass } = serverInfo;

// 	// 构建连接字符串
// 	const connectionString = getConnectionString(row, serverInfo);
// 	// 发送请求创建数据库
// 	const success = await CreateDatabase(connectionString, dbName, dbType);
// 	console.log(success);
// 	debugger;
// 	if (success.data.result == '数据库创建成功') {
// 		ElMessage.success('数据库创建成功');
// 		let values = row;
// 		values.ifCreat = 1;
// 		await updateBasCustomer(values);
// 		console.log(success.data);
// 		row.isDatabaseCreated = true;
// 		row.disabled = true;
// 	} else {
// 		ElMessage.error(success.data.result);
// 	}
// };

// //确定创建数据库弹窗
// const confirmDialog = (message, title) => {
// 	return new Promise((resolve) => {
// 		ElMessageBox.confirm(message, title, {
// 			confirmButtonText: '确定',
// 			cancelButtonText: '取消',
// 			type: 'warning',
// 		})
// 			.then(() => {
// 				resolve(true);
// 			})
// 			.catch(() => {
// 				resolve(false);
// 			});
// 	});
// };

// 列排序
const sortChange = async (column: any) => {
	// queryParams.value.field = column.prop;
	// queryParams.value.order = column.order;
	// await handleQuery();
};

// 打开新增页面
const openAddBasCustomer = () => {
	editBasCustomerTitle.value = '添加客户表';
	editDialogRef.value.openDialog({});
};

// 打开打印页面
const openPrintBasCustomer = async (row: any) => {
	printBasCustomerTitle.value = '打印客户表';
};

// 打开编辑页面
const openEditBasCustomer = (row: any) => {
	editBasCustomerTitle.value = '编辑客户表';
	editDialogRef.value.openDialog(row);
};

// 删除
const delBasCustomer = async (row: any) => {
	const { dbip, dbName, dbType, ifCreat } = row;
	// 从服务器获取登录用户信息
//	const res = await DbServerIP();
//	console.log(res);
//	const serverInfo = res.data.result.sysDbServer.find((server) => server.dbip === dbip);
//	const serverInfo = res.data.sysDbServer.find((server) => server.dbip === dbip);
//	const { logName, logPass } = serverInfo;
//	const response = await SelectValue();
//	const zhubiaoData = response.data.result.sysCusTbl.find((table) => table.dbip === dbip && table.dbName === dbName);
//	const zhubiaoData = response.data.sysCusTbl.find((table) => table.dbip === dbip && table.dbName === dbName);
//	console.log(zhubiaoData);
	ElMessageBox.confirm(`确定要删除吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
	//		console.log(zhubiaoData)
			//debugger;
	//		if (zhubiaoData) {
	//			ElMessage.error('该库已有数据，请先删除数据表！');
	//			return;
	//		}
			if (ifCreat === '0') {
				await deleteBasCustomer(row);
				handleQuery();
				ElMessage.success('删除成功');
				return;
			} else{

			}

			// 构建连接字符串
	//		const connectionString = getConnectionString(row, serverInfo);
			// 发送请求创建数据库
	//		const success = await DeleteDatabase(connectionString, dbName, dbType);
	//		console.log(success);
	//		if (success.data.result == '删除成功') {
	//			ElMessage.success('删除成功');
	//			await deleteBasCustomer(row);
	//			handleQuery();
	//			console.log(success.data);
	//		} else if (success.data.result.includes("无法对 数据库 '" + dbName + "' 执行 删除，因为它不存在，或者您没有所需的权限")) {
				// 如果返回的消息包含特定错误信息，执行删除操作
	//			await deleteBasCustomer(row);
	//			handleQuery();
	//			ElMessage.success('删除成功');
	//		} else {
	//			ElMessage.error(success.data.result);
	//		}
		})
		.catch(() => {});
};

// 改变页面容量
const handleSizeChange = (val: number) => {
	tableParams.value.pageSize = val;
	handleQuery();
};

// 改变页码序号
const handleCurrentChange = (val: number) => {
	tableParams.value.page = val;
	handleQuery();
};

// 计算属性，根据数据库类型的value返回对应的label
const getDbTypeLabel = (value: string) => {
	switch (value) {
		case '0':
			return 'SQL Server';
		case '1':
			return 'PostgreSQL';
		case '2':
			return 'MySql';
		default:
			return '';
	}
};
// 计算属性，根据是否停用的value返回对应的label
const getIFStopLabel = (value: string) => {
	switch (value) {
		case '0':
			return '否';
		case '1':
			return '是';
		default:
			return '';
	}
};

// 计算属性，根据数据库类型的value返回对应的连接字符串
const getConnectionString = (row: any, serverInfo: any) => {
	// 获取服务器IP和端口号
	const [serverIp, port] = row.dbip
		.split(',')
		.map((s) => s.trim())
		.filter(Boolean);
	switch (row.dbType) {
		case '0': // SQL Server
			return `Data Source=${row.dbip};Initial Catalog=master;User ID=${serverInfo.logName};Password=${serverInfo.logPass};`;
		case '1': // PostgreSQL
			return `Host=${serverIp};Port=${port};User ID=${serverInfo.logName};Password=${serverInfo.logPass};Database=${row.databaseName};`;
		case '2': // MySQL
			return `Server=${serverIp};Port=${port};Uid=${serverInfo.logName};password=${serverInfo.logPass};`;
		default:
			return '';
	}
};

handleQuery();
</script>
<style scoped>
:deep(.el-ipnut),
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}
</style>

