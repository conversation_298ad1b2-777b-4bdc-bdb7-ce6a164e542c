/* tslint:disable */
/* eslint-disable */
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。前后端分离架构(.NET6/Vue3)，开箱即用紧随前沿技术。<br/><a href='https://gitee.com/zuohuaijun/Admin.NET/'>https://gitee.com/zuohuaijun/Admin.NET</a>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { AccountTypeEnum } from './account-type-enum';
import { CardTypeEnum } from './card-type-enum';
import { CultureLevelEnum } from './culture-level-enum';
import { GenderEnum } from './gender-enum';
import { StatusEnum } from './status-enum';
import { SysOrg } from './sys-org';
 /**
 * 系统用户表
 *
 * @export
 * @interface UserInfo
 */
export interface UserInfo {

    /**
     * 用户ID
     *
     * @type {string}
     * @memberof UserInfo
     */
    UserId?: string;
    /**
     * 登录名
     *
     * @type {string}
     * @memberof UserInfo
     */
    LoginID?: string;
    /**
     * 密码
     *
     * @type {string}
     * @memberof UserInfo
     */
    PassWord?: number;
    /**
     * 角色类型
     *
     * @type {string}
     * @memberof UserInfo
     */
    RoleType?: string;
    /**
     * 单位名称
     *
     * @type {string}
     * @memberof UserInfo
     */
    Organization?: string;
}
