import request from '/@/utils/request';

enum Api {
  AddUserInfo = '/api/UserInfo/add',
  DeleteUserInfo = '/api/UserInfo/delete',
  UpdateUserInfo = '/api/UserInfo/update',
  PageUserInfo = '/api/UserInfo/page',
  DetailUserInfo = '/api/UserInfo/detail',
  ListUserInfo = '/api/UserInfo/list',
}
// 增加用户信息
export const addUserInfo = (params?: any) =>
	request({
		url: Api.AddUserInfo,
		method: 'post',
		data: params,
	});

// 删除用户信息
export const deleteUserInfo = (params?: any) => 
	request({
		url: Api.DeleteUserInfo,
		method: 'post',
		data: params,
	});

// 编辑用户信息
export const updateUserInfo = (params?: any) => 
	request({
		url: Api.UpdateUserInfo,
		method: 'post',
		data: params,
	});

// 分页查询用户信息
export const pageUserInfo = (params?: any) => 
	request({
		url: Api.PageUserInfo,
		method: 'post',
		data: params,
	});

// 详情用户信息
export const detailUserInfo = (id: any) => 
	request({
		url: Api.DetailUserInfo,
		method: 'get',
		data: { id },
	});

// 列表查询用户信息
export const listUserInfo = (params?: any) => 
	request({
		url: Api.ListUserInfo,
		method: 'get',
		data: params,
	});



