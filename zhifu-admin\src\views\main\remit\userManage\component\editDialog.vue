<template>
	<div class="basCustomer-container">
		<el-dialog v-model="isShowDialog" :width="800" draggable="">
			<template #header>
				<div style="color: #fff">
					<el-icon size="16" style="margin-right: 3px; display: inline; vertical-align: middle"> <ele-Edit /> </el-icon>
					<span>{{ props.title }}</span>
				</div>
			</template>
			<el-form :model="ruleForm" ref="ruleFormRef" label-width="auto" :rules="rules">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="登录名" prop="loginID">
							<el-input v-model="ruleForm.loginID" placeholder="请输入登录名" maxlength="50" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="密码" prop="passWord">
							<el-input v-model="ruleForm.passWord" type="password" placeholder="请输入密码" maxlength="50" show-word-limit clearable show-password />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="角色类型" prop="roleType">
							<el-select v-model="ruleForm.roleType" placeholder="请选择角色类型" clearable>
								<el-option key="1" label="管理员" value="1"></el-option>
								<el-option key="2" label="教育部门" value="2"></el-option>
								<el-option key="3" label="学校" value="3"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="单位名称" prop="organization">
							<el-input v-model="ruleForm.organization" placeholder="请输入单位名称" maxlength="100" show-word-limit clearable />
						</el-form-item>
					</el-col>

				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer" style="display: flex; justify-content: center">
					<el-button @click="cancel">取 消</el-button>
					<el-button type="primary" @click="submit">保 存</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>
<style scoped>
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}
</style>
<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { getDictDataItem as di, getDictDataList as dl } from '/@/utils/dict-utils';
import { ElMessage } from 'element-plus';
import type { FormRules } from 'element-plus';
import { addBasCustomer, updateBasCustomer, detailBasCustomer, DbServerIP } from '/@/api/main/basCustomer';
import { useUserInfo } from '/@/stores/userInfo';
import { storeToRefs } from 'pinia';
import { Session } from '/@/utils/storage';

//父级传递来的参数
var props = defineProps({
	title: {
		type: String,
		default: '',
	},
});
//父级传递来的函数，用于回调
const emit = defineEmits(['reloadTable']);
const ruleFormRef = ref();
const isShowDialog = ref(false);
const ruleForm = ref<any>({});
const stores = useUserInfo();
const { userInfos } = storeToRefs(stores);
// 密码复杂度验证函数
const validatePassword = (rule: any, value: string, callback: any) => {
	if (!value) {
		callback(new Error('请输入密码'));
		return;
	}

	// 检查密码长度
	// if (value.length < 8) {
	// 	callback(new Error('密码长度至少8位'));
	// 	return;
	// }

	// 检查密码复杂度：至少包含大小写字母、数字、特殊符号中的三种
	// let typeCount = 0;
	// if (/[a-z]/.test(value)) typeCount++; // 小写字母
	// if (/[A-Z]/.test(value)) typeCount++; // 大写字母
	// if (/[0-9]/.test(value)) typeCount++; // 数字
	// if (/[^a-zA-Z0-9]/.test(value)) typeCount++; // 特殊符号

	// if (typeCount < 3) {
	// 	callback(new Error('密码必须包含大小写字母、数字、特殊符号中的至少三种'));
	// 	return;
	// }

	callback();
};

// 表单验证规则
const rules = ref<FormRules>({
	loginID: [
		{ required: true, message: '请输入登录名', trigger: 'blur' },
		// { min: 3, max: 50, message: '登录名长度在3到50个字符', trigger: 'blur' }
	],
	passWord: [
		{ required: true, validator: validatePassword, trigger: 'blur' }
	],
	roleType: [
		{ required: true, message: '请选择角色类型', trigger: 'change' }
	],
	organization: [
		{ required: true, message: '请输入单位名称', trigger: 'blur' },
		// { min: 2, max: 100, message: '单位名称长度在2到100个字符', trigger: 'blur' }
	]
});



// 打开弹窗
const openDialog = async (row: any) => {
  	 let rowData = JSON.parse(JSON.stringify(row));
 	 console.log("rowData 数据:", rowData);
	console.log("22222222222" + rowData);
	console.log("33333333333333333" + rowData.id);
	if (rowData.id) {
		//debugger
		ruleForm.value = (await axios.get(window.__env__.VITE_API_URL+'/api/basCustomer/detail?id='+rowData.id)).data;
		console.log(ruleForm.value +"basCustomer的ruleForm.value ");
	}
	else{
		ruleForm.value = {
			ifStop: 0, // 新增时设置默认值或空对象
			ifCreat: 0,
		};
	}
	isShowDialog.value = true;
};

// 关闭弹窗
const closeDialog = () => {
	emit('reloadTable');
	isShowDialog.value = false;
};

// 取消
const cancel = () => {
	isShowDialog.value = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			let values = ruleForm.value;
			if (ruleForm.value.id == undefined || ruleForm.value.id == null || ruleForm.value.id == '' || ruleForm.value.id == 0) {
				values.createDate = new Date();
				values.updateDate = new Date();
				values.auStaff = Session.get('userName') === 'superadmin' ? '超级管理员' : Session.get('userName');
				values.upStaff = Session.get('userName') === 'superadmin' ? '超级管理员' : Session.get('userName');
				await addBasCustomer(values);
			} else {
				values.updateDate = new Date();
				values.upStaff = Session.get('userName') === 'superadmin' ? '超级管理员' : Session.get('userName');
				await updateBasCustomer(values);
			}
			closeDialog();
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: 'error',
			});
		}
	});
};


// 页面加载时
onMounted(async () => {});

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>




