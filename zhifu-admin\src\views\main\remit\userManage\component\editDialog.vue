<template>
	<div class="basCustomer-container">
		<el-dialog v-model="isShowDialog" :width="800" draggable="">
			<template #header>
				<div style="color: #fff">
					<el-icon size="16" style="margin-right: 3px; display: inline; vertical-align: middle"> <ele-Edit /> </el-icon>
					<span>{{ props.title }}</span>
				</div>
			</template>
			<el-form :model="ruleForm" ref="ruleFormRef" label-width="auto" :rules="rules">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="客户名称" prop="cusName">
							<el-input v-model="ruleForm.cusName" placeholder="请输入客户名称" maxlength="300" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="大屏展示地址" prop="showUrl">
							<el-input v-model="ruleForm.showUrl" placeholder="请输入大屏展示地址" maxlength="200" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="业务系统地址" prop="flowUrl">
							<el-input v-model="ruleForm.flowUrl" placeholder="请输入业务系统地址" maxlength="200" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="数据库服务器IP" prop="dbip">
							<el-select v-model="ruleForm.dbip" placeholder="请输入数据库服务器IP" clearable :disabled="ruleForm.ifCreat === '1'">
								<el-option v-for="item in IPData" :key="item.dbip" :label="item.dbip" :value="item.dbip"> </el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="数据库类型" prop="dbType">
							<el-select v-model="ruleForm.dbType" placeholder="请选择数据库类型" clearable :disabled="ruleForm.ifCreat === '1'">
								<el-option key="0" label="SQL Server" value="0"></el-option>
								<el-option key="1" label="PostgreSQL" value="1"></el-option>
								<el-option key="2" label="MySql" value="2"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col v-if="ruleForm.dbType === '1'" :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="数据库名" prop="databaseName">
							<el-input v-model="ruleForm.databaseName" placeholder="请输入数据库名"  show-word-limit clearable :disabled="ruleForm.ifCreat === '1'" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" v-if="ruleForm.id">
						<el-form-item label="数据库名称" prop="dBName">
							<el-input v-model="ruleForm.dbName" placeholder="请输入数据库名称" maxlength="30" show-word-limit clearable :disabled="ruleForm.ifCreat === '1'" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" v-if="ruleForm.id">
						<el-form-item label="是否停用" prop="ifStop">
							<el-select v-model="ruleForm.ifStop" placeholder="请选择是否停用" clearable>
								<el-option label="是" value="1"></el-option>
								<el-option label="否" value="0"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" v-if="ruleForm.id">
						<el-form-item label="建档日期" prop="createDate">
							<el-date-picker v-model="ruleForm.createDate" type="date" placeholder="建档日期" :disabled="ruleForm.id" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" v-if="ruleForm.id">
						<el-form-item label="建档人" prop="auStaff">
							<el-input v-model="ruleForm.auStaff" placeholder="请输入建档人" maxlength="5" show-word-limit clearable :disabled="ruleForm.id" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" v-if="ruleForm.id">
						<el-form-item label="更新日期" prop="updateDate">
							<el-date-picker v-model="ruleForm.updateDate" type="date" placeholder="更新日期" :disabled="ruleForm.id" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" v-if="ruleForm.id">
						<el-form-item label="更新人" prop="upStaff">
							<el-input v-model="ruleForm.upStaff" placeholder="请输入更新人" maxlength="5" show-word-limit clearable :disabled="ruleForm.id" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" v-if="ruleForm.id">
						<el-form-item label="客户ID">
							<el-input-number v-model="ruleForm.c_ID" placeholder="请输入客户ID" clearable :disabled="ruleForm.id" />
						</el-form-item>
					</el-col>
					<el-form-item v-show="false">
						<el-input v-model="ruleForm.id" />
					</el-form-item>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer" style="display: flex; justify-content: center">
					<el-button @click="cancel">取 消</el-button>
					<el-button type="primary" @click="submit">保 存</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>
<style scoped>
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}
</style>
<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { getDictDataItem as di, getDictDataList as dl } from '/@/utils/dict-utils';
import { ElMessage } from 'element-plus';
import type { FormRules } from 'element-plus';
import { addBasCustomer, updateBasCustomer, detailBasCustomer, DbServerIP } from '/@/api/main/basCustomer';
import { useUserInfo } from '/@/stores/userInfo';
import { storeToRefs } from 'pinia';
import { Session } from '/@/utils/storage';

//父级传递来的参数
var props = defineProps({
	title: {
		type: String,
		default: '',
	},
});
//父级传递来的函数，用于回调
const emit = defineEmits(['reloadTable']);
const ruleFormRef = ref();
const isShowDialog = ref(false);
const ruleForm = ref<any>({});
const stores = useUserInfo();
const { userInfos } = storeToRefs(stores);
//自行添加其他规则
const rules = ref<FormRules>({
	c_ID: [{ required: true, message: '请输入客户ID！', trigger: 'blur' }],
	dbip: [{ required: true, message: '请选择数据库服务器IP', trigger: 'change' }],
  	dbType: [{ required: true, message: '请选择数据库类型', trigger: 'change' }],
});

//获取服务器IP的下拉框
const IPData = ref([]); //服务器IP
const fetchOptions = async () => {
try {
	// const response = await DbServerIP(); // 调用API获取下拉框的值
	// console.log(response);
	// IPData.value = response.data.sysDbServer;

} catch (error) {
	console.error('获取下拉框值失败:', error);
	}
};
onMounted(fetchOptions);

// 打开弹窗
const openDialog = async (row: any) => {
  	 let rowData = JSON.parse(JSON.stringify(row));
 	 console.log("rowData 数据:", rowData);
	console.log("22222222222" + rowData);
	console.log("33333333333333333" + rowData.id);
	if (rowData.id) {
		//debugger
		ruleForm.value = (await axios.get(window.__env__.VITE_API_URL+'/api/basCustomer/detail?id='+rowData.id)).data;
		console.log(ruleForm.value +"basCustomer的ruleForm.value ");
	}
	else{
		ruleForm.value = {
			ifStop: 0, // 新增时设置默认值或空对象
			ifCreat: 0,
		};
	}
	isShowDialog.value = true;
};

// 关闭弹窗
const closeDialog = () => {
	emit('reloadTable');
	isShowDialog.value = false;
};

// 取消
const cancel = () => {
	isShowDialog.value = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			let values = ruleForm.value;
			
			if (values.dbType == "1" && values.databaseName == null) {
				ElMessage({
					message: "数据库名不能为空！",
					type: "error",
				});
				return;
			}
			if (ruleForm.value.id == undefined || ruleForm.value.id == null || ruleForm.value.id == '' || ruleForm.value.id == 0) {
				values.createDate = new Date();
				values.updateDate = new Date();
				values.auStaff = Session.get('userName') === 'superadmin' ? '超级管理员' : Session.get('userName');
				values.upStaff = Session.get('userName') === 'superadmin' ? '超级管理员' : Session.get('userName');
				await addBasCustomer(values);
			} else {
				values.updateDate = new Date();
				values.upStaff = Session.get('userName') === 'superadmin' ? '超级管理员' : Session.get('userName');
				await updateBasCustomer(values);
			}
			closeDialog();
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: 'error',
			});
		}
	});
};


// 页面加载时
onMounted(async () => {});

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>




