<template>
	<div class="login-container flex">
		<div class="login-right flex">
			<div class="login-right-warp flex-margin" style="margin-left: 10%; background-color: rgba(0, 0, 0, 0.5); border-radius: 20px;">
				<div class="login-right-warp-main">
					<div class="login-right-warp-main-title">黄浦区教育缴费</div>
					<div class="login-right-warp-main-form">
						<Account />
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts" name="payLoadLogin">
import { defineAsyncComponent } from 'vue';

// 引入组件
const Account = defineAsyncComponent(() => import('/@/views/login/component/account.vue'));
</script>

<style scoped lang="scss">
.login-container {
	height: 100%;
	background-image: url('/@/assets/bg-img2.png'), url('/@/assets/bg-img1.jpg');
	background-size: 70%, 105%; 
	background-repeat: no-repeat;
	background-position: right 70%, center;

	.login-left {
		flex: 1;
		.login-left-logo {
			position: absolute;
			top: 30px;
			left: 30px;
			display: flex;
			align-items: center;
			&-text {
				padding-left: 10px;
				&-msg {
					display: block;
					margin-top: 5px;
					font-size: 12px;
					color: var(--el-text-color-secondary);
					letter-spacing: 4px;
				}
			}
		}
	}
	.login-right {
		width: 700px;
		&-warp {
			position: relative;
			overflow: hidden;
			width: 500px;
			height: 500px;
			padding: 20px;
			box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
			&-main {
				margin-top: 100px;
				&-title {
					font-size: 24px;
					color: #ffffff;
					text-align: center;
					letter-spacing: 2px;
					margin-bottom: 30px;
				}
				&-form {
					position: relative;
					margin: 0 auto;
					width: 80%;
				}
			}
		}
	}
}
</style>